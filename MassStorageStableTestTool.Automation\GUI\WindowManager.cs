using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using FlaUI.UIA3;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// 窗口管理器，负责查找、激活和控制应用程序窗口
/// </summary>
public class WindowManager : IDisposable
{
    private readonly ILogger<WindowManager> _logger;
    private readonly UIA3Automation _automation;
    private readonly Dictionary<string, Window> _cachedWindows;
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public WindowManager(ILogger<WindowManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _automation = new UIA3Automation();
        _cachedWindows = new Dictionary<string, Window>();
    }

    /// <summary>
    /// 根据进程ID查找窗口
    /// </summary>
    /// <param name="processId">进程ID</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByProcessId(int processId, TimeSpan timeout = default)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                if (process.MainWindowHandle != IntPtr.Zero)
                {
                    var window = _automation.FromHandle(process.MainWindowHandle).AsWindow();
                    if (window != null)
                    {
                        _logger.LogDebug($"找到进程 {processId} 的窗口: {window.Title}");
                        return window;
                    }
                }
            }
            catch (ArgumentException)
            {
                // 进程不存在
                _logger.LogWarning($"进程 {processId} 不存在");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找进程 {processId} 的窗口时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到进程 {processId} 的窗口");
        return null;
    }

    /// <summary>
    /// 根据窗口标题查找窗口
    /// </summary>
    /// <param name="title">窗口标题</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="exactMatch">是否精确匹配</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByTitle(string title, TimeSpan timeout = default, bool exactMatch = false)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("窗口标题不能为空", nameof(title));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var cacheKey = $"title_{title}_{exactMatch}";
        if (_cachedWindows.TryGetValue(cacheKey, out var cachedWindow) && IsWindowValid(cachedWindow))
        {
            return cachedWindow;
        }

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var desktop = _automation.GetDesktop();
                var windows = desktop.FindAllChildren(cf => cf.ByControlType(ControlType.Window));

                foreach (var window in windows)
                {
                    var windowTitle = window.AsWindow().Title;
                    if (string.IsNullOrEmpty(windowTitle))
                        continue;

                    bool matches = exactMatch 
                        ? windowTitle.Equals(title, StringComparison.OrdinalIgnoreCase)
                        : windowTitle.Contains(title, StringComparison.OrdinalIgnoreCase);

                    if (matches)
                    {
                        var windowElement = window.AsWindow();
                        _cachedWindows[cacheKey] = windowElement;
                        _logger.LogDebug($"找到窗口: {windowTitle}");
                        return windowElement;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找窗口 '{title}' 时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到标题包含 '{title}' 的窗口");
        return null;
    }

    /// <summary>
    /// 根据类名查找窗口
    /// </summary>
    /// <param name="className">窗口类名</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>窗口对象</returns>
    public Window? FindWindowByClassName(string className, TimeSpan timeout = default)
    {
        if (string.IsNullOrWhiteSpace(className))
            throw new ArgumentException("窗口类名不能为空", nameof(className));

        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        var cacheKey = $"class_{className}";
        if (_cachedWindows.TryGetValue(cacheKey, out var cachedWindow) && IsWindowValid(cachedWindow))
        {
            return cachedWindow;
        }

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                var desktop = _automation.GetDesktop();
                var window = desktop.FindFirstChild(cf => cf.ByClassName(className))?.AsWindow();
                
                if (window != null)
                {
                    _cachedWindows[cacheKey] = window;
                    _logger.LogDebug($"找到类名为 '{className}' 的窗口: {window.Title}");
                    return window;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查找类名为 '{className}' 的窗口时出错: {ex.Message}");
            }

            Thread.Sleep(500);
        }

        _logger.LogError($"在 {timeout.TotalSeconds} 秒内未找到类名为 '{className}' 的窗口");
        return null;
    }

    /// <summary>
    /// 激活窗口
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <returns>是否成功激活</returns>
    public bool ActivateWindow(Window window)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        try
        {
            // 检查窗口是否最小化，如果是则恢复
            var windowPattern = window.Patterns.Window.PatternOrDefault;
            if (windowPattern != null && windowPattern.WindowVisualState.Value == WindowVisualState.Minimized)
            {
                windowPattern.SetWindowVisualState(WindowVisualState.Normal);
                Thread.Sleep(500);
            }

            // 激活窗口
            window.Focus();
            window.SetForeground();

            _logger.LogDebug($"成功激活窗口: {window.Title}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"激活窗口 '{window.Title}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 关闭窗口
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <param name="force">是否强制关闭</param>
    /// <returns>是否成功关闭</returns>
    public bool CloseWindow(Window window, bool force = false)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        try
        {
            if (force)
            {
                // 强制关闭：尝试终止进程
                var processId = window.Properties.ProcessId.Value;
                var process = Process.GetProcessById(processId);
                process.Kill();
                process.WaitForExit(5000);
            }
            else
            {
                // 正常关闭：发送关闭消息
                window.Close();
            }

            _logger.LogDebug($"成功关闭窗口: {window.Title}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"关闭窗口 '{window.Title}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 等待窗口关闭
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否在超时时间内关闭</returns>
    public bool WaitForWindowClosed(Window window, TimeSpan timeout)
    {
        if (window == null)
            throw new ArgumentNullException(nameof(window));

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                if (!IsWindowValid(window))
                {
                    _logger.LogDebug($"窗口已关闭: {window.Title}");
                    return true;
                }
            }
            catch
            {
                // 窗口已关闭，访问属性会抛出异常
                return true;
            }

            Thread.Sleep(500);
        }

        _logger.LogWarning($"窗口 '{window.Title}' 在 {timeout.TotalSeconds} 秒内未关闭");
        return false;
    }

    /// <summary>
    /// 检查窗口是否有效
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <returns>是否有效</returns>
    private bool IsWindowValid(Window window)
    {
        try
        {
            return window != null && window.IsAvailable && !string.IsNullOrEmpty(window.Title);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 清理缓存的窗口
    /// </summary>
    public void ClearCache()
    {
        _cachedWindows.Clear();
        _logger.LogDebug("已清理窗口缓存");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cachedWindows.Clear();
            _automation?.Dispose();
            _disposed = true;
        }
    }
}
