using FlaUI.Core.AutomationElements;
using MassStorageStableTestTool.Automation.GUI;
using Microsoft.Extensions.Logging;
using Moq;

namespace MassStorageStableTestTool.Tests.TestHelpers;

/// <summary>
/// Mock对象创建辅助类
/// </summary>
public static class MockHelper
{
    /// <summary>
    /// 创建AutomationHelper的Mock对象
    /// </summary>
    /// <returns>AutomationHelper Mock</returns>
    public static Mock<AutomationHelper> CreateMockAutomationHelper()
    {
        var mockWindowManager = CreateMockWindowManager();
        var mockElementFinder = CreateMockElementFinder();
        var mockInputSimulator = CreateMockInputSimulator();
        var mockScreenCapture = CreateMockScreenCapture();
        var mockLogger = new Mock<ILogger<AutomationHelper>>();

        return new Mock<AutomationHelper>(
            mockLogger.Object,
            mockWindowManager.Object,
            mockElementFinder.Object,
            mockInputSimulator.Object,
            mockScreenCapture.Object);
    }

    /// <summary>
    /// 创建WindowManager的Mock对象
    /// </summary>
    /// <returns>WindowManager Mock</returns>
    public static Mock<WindowManager> CreateMockWindowManager()
    {
        var mockLogger = new Mock<ILogger<WindowManager>>();
        var mock = new Mock<WindowManager>(mockLogger.Object);

        // 设置默认行为
        mock.Setup(x => x.FindWindowByTitle(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .Returns((Window?)null);

        mock.Setup(x => x.FindWindowByProcessId(It.IsAny<int>(), It.IsAny<TimeSpan>()))
            .Returns((Window?)null);

        mock.Setup(x => x.ActivateWindow(It.IsAny<Window>()))
            .Returns(true);

        mock.Setup(x => x.CloseWindow(It.IsAny<Window>(), It.IsAny<bool>()))
            .Returns(true);

        return mock;
    }

    /// <summary>
    /// 创建ElementFinder的Mock对象
    /// </summary>
    /// <returns>ElementFinder Mock</returns>
    public static Mock<ElementFinder> CreateMockElementFinder()
    {
        var mockLogger = new Mock<ILogger<ElementFinder>>();
        var mock = new Mock<ElementFinder>(mockLogger.Object);

        // 设置默认行为
        mock.Setup(x => x.FindElementByName(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .Returns((AutomationElement?)null);

        mock.Setup(x => x.FindElementByAutomationId(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .Returns((AutomationElement?)null);

        return mock;
    }

    /// <summary>
    /// 创建InputSimulator的Mock对象
    /// </summary>
    /// <returns>InputSimulator Mock</returns>
    public static Mock<InputSimulator> CreateMockInputSimulator()
    {
        var mockLogger = new Mock<ILogger<InputSimulator>>();
        var mock = new Mock<InputSimulator>(mockLogger.Object);

        // 设置默认行为
        mock.Setup(x => x.ClickElement(It.IsAny<AutomationElement>(), It.IsAny<bool>()))
            .ReturnsAsync(true);

        mock.Setup(x => x.TypeText(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<bool>()))
            .Returns(true);

        return mock;
    }

    /// <summary>
    /// 创建ScreenCapture的Mock对象
    /// </summary>
    /// <returns>ScreenCapture Mock</returns>
    public static Mock<ScreenCapture> CreateMockScreenCapture()
    {
        var mockLogger = new Mock<ILogger<ScreenCapture>>();
        var mock = new Mock<ScreenCapture>(mockLogger.Object);

        // 设置默认行为
        mock.Setup(x => x.CaptureScreen(It.IsAny<string>(), It.IsAny<System.Drawing.Imaging.ImageFormat>()))
            .Returns(true);

        mock.Setup(x => x.CaptureWindow(It.IsAny<Window>(), It.IsAny<string>(), It.IsAny<System.Drawing.Imaging.ImageFormat>()))
            .Returns(true);

        return mock;
    }

    /// <summary>
    /// 创建Window的Mock对象
    /// </summary>
    /// <returns>Window Mock</returns>
    public static Mock<Window> CreateMockWindow()
    {
        var mock = new Mock<Window>();

        // 设置默认属性
        mock.Setup(x => x.Title).Returns("Test Window");
        mock.Setup(x => x.IsAvailable).Returns(true);

        return mock;
    }

    /// <summary>
    /// 创建AutomationElement的Mock对象
    /// </summary>
    /// <returns>AutomationElement Mock</returns>
    public static Mock<AutomationElement> CreateMockAutomationElement()
    {
        var mock = new Mock<AutomationElement>();

        // 设置默认属性
        mock.Setup(x => x.Name).Returns("Test Element");
        mock.Setup(x => x.IsAvailable).Returns(true);

        return mock;
    }

    /// <summary>
    /// 创建Logger的Mock对象
    /// </summary>
    /// <typeparam name="T">Logger类型</typeparam>
    /// <returns>Logger Mock</returns>
    public static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        var mock = new Mock<ILogger<T>>();

        // 设置默认行为，使Log方法不抛出异常
        mock.Setup(x => x.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()));

        return mock;
    }

    /// <summary>
    /// 验证Logger是否被调用
    /// </summary>
    /// <typeparam name="T">Logger类型</typeparam>
    /// <param name="mockLogger">Mock Logger</param>
    /// <param name="logLevel">日志级别</param>
    /// <param name="times">调用次数</param>
    public static void VerifyLoggerCalled<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, Times times)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times);
    }

    /// <summary>
    /// 验证Logger是否记录了特定消息
    /// </summary>
    /// <typeparam name="T">Logger类型</typeparam>
    /// <param name="mockLogger">Mock Logger</param>
    /// <param name="logLevel">日志级别</param>
    /// <param name="message">消息内容</param>
    /// <param name="times">调用次数</param>
    public static void VerifyLoggerMessage<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, string message, Times times)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times);
    }

    /// <summary>
    /// 设置AutomationHelper的成功行为
    /// </summary>
    /// <param name="mockAutomationHelper">AutomationHelper Mock</param>
    public static void SetupSuccessfulAutomationHelper(Mock<AutomationHelper> mockAutomationHelper)
    {
        var mockWindow = CreateMockWindow();
        var mockElement = CreateMockAutomationElement();

        mockAutomationHelper.Setup(x => x.WaitForWindow(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .Returns(mockWindow.Object);

        mockAutomationHelper.Setup(x => x.FindAndClickButton(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .ReturnsAsync(true);

        mockAutomationHelper.Setup(x => x.FindAndSetTextBox(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .Returns(true);

        mockAutomationHelper.Setup(x => x.FindAndSelectComboBoxOption(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .ReturnsAsync(true);

        mockAutomationHelper.Setup(x => x.CaptureForDebugging(It.IsAny<Window>(), It.IsAny<string>(), It.IsAny<string>()))
            .Returns("test_screenshot.png");
    }

    /// <summary>
    /// 设置AutomationHelper的失败行为
    /// </summary>
    /// <param name="mockAutomationHelper">AutomationHelper Mock</param>
    public static void SetupFailedAutomationHelper(Mock<AutomationHelper> mockAutomationHelper)
    {
        mockAutomationHelper.Setup(x => x.WaitForWindow(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .Returns((Window?)null);

        mockAutomationHelper.Setup(x => x.FindAndClickButton(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .ReturnsAsync(false);

        mockAutomationHelper.Setup(x => x.FindAndSetTextBox(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>()))
            .Returns(false);

        mockAutomationHelper.Setup(x => x.FindAndSelectComboBoxOption(It.IsAny<AutomationElement>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .ReturnsAsync(false);
    }
}
