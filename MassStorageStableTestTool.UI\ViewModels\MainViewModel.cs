using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MassStorageStableTestTool.UI.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using LogLevel = MassStorageStableTestTool.UI.Models.LogLevel;
using MassStorageStableTestTool.UI.Resources;

namespace MassStorageStableTestTool.UI.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;

        [ObservableProperty]
        private ObservableCollection<DriveInfo> _availableDrives = new();

        [ObservableProperty]
        private int _selectedDriveCount;

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _guiTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _cliTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _hybridTools = new();

        [ObservableProperty]
        private ObservableCollection<LogEntry> _logEntries = new();

        [ObservableProperty]
        private string _currentStatus = Strings.Status_WaitingStart;

        [ObservableProperty]
        private double _overallProgress = 0;

        [ObservableProperty]
        private string _currentTestName = string.Empty;

        [ObservableProperty]
        private double _currentTestProgress = 0;

        [ObservableProperty]
        private string _estimatedTimeRemaining = string.Empty;

        [ObservableProperty]
        private bool _isTestRunning = false;

        [ObservableProperty]
        private string _statusBarText = Strings.Status_Ready;

        [ObservableProperty]
        private double _cpuUsage = 0;

        [ObservableProperty]
        private double _memoryUsage = 0;

        public MainViewModel(ILogger<MainViewModel> logger)
        {
            _logger = logger;
            InitializeTestTools();
            RefreshDrives();
        }

        public bool CanStartTest => SelectedDriveCount > 0 && !IsTestRunning && HasSelectedTools;

        public bool HasSelectedTools =>
            GuiTools.Any(t => t.IsSelected) ||
            CliTools.Any(t => t.IsSelected) ||
            HybridTools.Any(t => t.IsSelected);

        public bool CanSelectAll => AvailableDrives.Any(d => !d.IsSelected);
        public bool CanDeselectAll => AvailableDrives.Any(d => d.IsSelected);

        public string TestSummary
        {
            get
            {
                var selectedToolCount = GuiTools.Count(t => t.IsSelected) +
                                      CliTools.Count(t => t.IsSelected) +
                                      HybridTools.Count(t => t.IsSelected);

                // return $"• 选中设备: {SelectedDriveCount} 个\n" +
                //        $"• 选中工具: {selectedToolCount} 个\n" +
                //        $"• 预计时间: 约 {EstimateTestTime()} 小时\n" +
                //        $"• 测试模式: 并行测试";
                string formatTemplate = Strings.TestSummaryFormat;
                return string.Format(formatTemplate,
                    SelectedDriveCount,
                    selectedToolCount,
                    EstimateTestTime());
            }
        }

        [RelayCommand]
        private void RefreshDrives()
        {
            try
            {
                AvailableDrives.Clear();

                var drives = System.IO.DriveInfo.GetDrives()
                    .Where(d => d.DriveType == System.IO.DriveType.Removable && d.IsReady)
                    .Select(d => new DriveInfo
                    {
                        Name = d.Name,
                        Label = d.VolumeLabel,
                        TotalSize = d.TotalSize,
                        AvailableSpace = d.AvailableFreeSpace,
                        IsReady = d.IsReady,
                        Status = d.IsReady ? Strings.Status_Ready : Strings.Status_NotReady
                    });

                foreach (var drive in drives)
                {
                    drive.PropertyChanged += Drive_PropertyChanged;
                    AvailableDrives.Add(drive);
                }

                UpdateSelectedDriveCount();

                AddLogEntry(LogLevel.Info, string.Format(Strings.Log_FoundRemovableDevice, AvailableDrives.Count));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新驱动器列表时出错");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Error_RefreshDrives, ex.Message));
            }
        }

        [RelayCommand]
        private async Task StartTestAsync()
        {
            if (!CanStartTest) return;

            try
            {
                IsTestRunning = true;
                CurrentStatus = Strings.Status_PreparingTest;
                AddLogEntry(LogLevel.Info, Strings.Log_BeginTestExecution);

                // 模拟测试执行
                await SimulateTestExecution();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试执行失败");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Log_TestExecutionFailed, ex.Message));
            }
            finally
            {
                IsTestRunning = false;
                CurrentStatus = Strings.Status_TestCompleted;
            }
        }

        [RelayCommand]
        private void StopTest()
        {
            if (!IsTestRunning) return;

            IsTestRunning = false;
            CurrentStatus = Strings.Status_TestStopped;
            AddLogEntry(LogLevel.Warning, Strings.Log_UserStoppedTest);
        }

        [RelayCommand]
        private void OpenSettings()
        {
            try
            {
                var settingsWindow = new Views.SettingsWindow();
                var result = settingsWindow.ShowDialog();

                if (result == true)
                {
                    AddLogEntry(LogLevel.Info, Strings.Log_SettingSaved);
                }
                else
                {
                    AddLogEntry(LogLevel.Info, Strings.Log_SettingCancelled);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开设置窗口失败");
                AddLogEntry(LogLevel.Error, string.Format(Strings.Log_OpenSettingWindowsFail, ex.Message));
            }
        }

        [RelayCommand]
        private void ClearLog()
        {
            LogEntries.Clear();
            AddLogEntry(LogLevel.Info, Strings.Log_ClearLog);
        }

        [RelayCommand]
        private void SaveLog()
        {
            // TODO: 实现保存日志功能
            AddLogEntry(LogLevel.Info, Strings.Log_SaveLogToFile);
        }

        [RelayCommand]
        private void SelectAllDrives()
        {
            foreach (var drive in AvailableDrives)
            {
                drive.IsSelected = true;
                drive.TestStatus = DriveTestStatus.Selected;
            }
            UpdateSelectedDriveCount();
            AddLogEntry(LogLevel.Info, string.Format(Strings.Log_SelectedDrives, AvailableDrives.Count));
        }

        [RelayCommand]
        private void DeselectAllDrives()
        {
            foreach (var drive in AvailableDrives)
            {
                drive.IsSelected = false;
                drive.TestStatus = DriveTestStatus.Ready;
            }
            UpdateSelectedDriveCount();
            AddLogEntry(LogLevel.Info, Strings.Log_AllDrivesDeselected);
        }

        [RelayCommand]
        private void ToggleDriveSelection(DriveInfo drive)
        {
            if (drive != null)
            {
                drive.IsSelected = !drive.IsSelected;
                drive.TestStatus = drive.IsSelected ? DriveTestStatus.Selected : DriveTestStatus.Ready;
                UpdateSelectedDriveCount();
                AddLogEntry(LogLevel.Info, drive.IsSelected
                    ? string.Format(Strings.Log_DriverSelected, drive.Name)
                    : string.Format(Strings.Log_DriverDeselected, drive.Name));
            }
        }

        private void InitializeTestTools()
        {
            // GUI工具
            GuiTools.Add(new TestToolViewModel
            {
                Name = "H2Testw",
                DisplayName = "H2testw (完整性测试)",
                Description = "SD卡完整性和真实容量测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.Available
            });

            GuiTools.Add(new TestToolViewModel
            {
                Name = "CrystalDiskMark",
                DisplayName = "CrystalDiskMark (性能基准)",
                Description = "磁盘性能基准测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.Available
            });

            GuiTools.Add(new TestToolViewModel
            {
                Name = "ATTO",
                DisplayName = "ATTO Disk Benchmark",
                Description = "不同块大小的读写性能测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.NotAvailable
            });

            // CLI工具
            CliTools.Add(new TestToolViewModel
            {
                Name = "fio",
                DisplayName = "fio (高性能I/O测试)",
                Description = "灵活的I/O性能测试工具",
                ToolType = TestToolType.CLI,
                Status = TestToolStatus.Available
            });

            CliTools.Add(new TestToolViewModel
            {
                Name = "diskspd",
                DisplayName = "diskspd (Windows原生)",
                Description = "Microsoft官方磁盘性能测试工具",
                ToolType = TestToolType.CLI,
                Status = TestToolStatus.NotAvailable
            });

            // 混合工具
            HybridTools.Add(new TestToolViewModel
            {
                Name = "HDTunePro",
                DisplayName = "HD Tune Pro",
                Description = "综合磁盘测试工具",
                ToolType = TestToolType.Hybrid,
                Status = TestToolStatus.NotConfigured
            });

            // 为所有工具添加属性变化监听
            foreach (var tool in GuiTools.Concat(CliTools).Concat(HybridTools))
            {
                tool.PropertyChanged += Tool_PropertyChanged;
            }
        }

        private async Task SimulateTestExecution()
        {
            var selectedTools = GuiTools.Concat(CliTools).Concat(HybridTools)
                .Where(t => t.IsSelected).ToList();
            var selectedDrives = AvailableDrives.Where(d => d.IsSelected).ToList();

            if (!selectedTools.Any() || !selectedDrives.Any()) return;

            // 并行测试所有选中的设备
            var testTasks = selectedDrives.Select(drive => TestSingleDriveAsync(drive, selectedTools)).ToArray();

            // 等待所有设备测试完成
            await Task.WhenAll(testTasks);

            OverallProgress = 100;
            AddLogEntry(LogLevel.Info, string.Format(Strings.Log_GenerateReport, selectedDrives.Count));
        }

        private async Task TestSingleDriveAsync(DriveInfo drive, List<TestToolViewModel> tools)
        {
            drive.TestStatus = DriveTestStatus.Testing;
            AddLogEntry(LogLevel.Info, string.Format(Strings.Log_DriverTestBegin, drive.Name));

            for (int i = 0; i < tools.Count; i++)
            {
                if (!IsTestRunning) break;

                var tool = tools[i];
                drive.CurrentTest = tool.Name;
                tool.Status = TestToolStatus.Testing;

                AddLogEntry(LogLevel.Info, string.Format(Strings.Log_ToolTestBegin, drive.Name, tool.Name));

                // 模拟测试进度
                for (int progress = 0; progress <= 100; progress += 10)
                {
                    drive.TestProgress = ((i * 100) + progress) / tools.Count;
                    tool.Progress = progress;

                    // 更新总体进度
                    UpdateOverallProgress();

                    await Task.Delay(300); // 模拟测试时间

                    if (!IsTestRunning) return;
                }

                tool.Status = TestToolStatus.Completed;
                AddLogEntry(LogLevel.Info, string.Format(Strings.Log_ToolTestCompleted, drive.Name, tool.Name));
            }

            drive.TestStatus = DriveTestStatus.Completed;
            drive.TestProgress = 100;
            drive.CurrentTest = Strings.Status_Completed;
            AddLogEntry(LogLevel.Info, string.Format(Strings.Log_DriverTestCompleted, drive.Name));
        }

        private void UpdateOverallProgress()
        {
            var selectedDrives = AvailableDrives.Where(d => d.IsSelected).ToList();
            if (selectedDrives.Any())
            {
                OverallProgress = selectedDrives.Average(d => d.TestProgress);
            }
        }

        private void AddLogEntry(LogLevel level, string message)
        {
            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                Source = "MainViewModel"
            };

            LogEntries.Add(entry);

            // 限制日志条目数量
            while (LogEntries.Count > 1000)
            {
                LogEntries.RemoveAt(0);
            }
        }

        private string EstimateTestTime()
        {
            var selectedToolCount = GuiTools.Count(t => t.IsSelected) +
                                  CliTools.Count(t => t.IsSelected) +
                                  HybridTools.Count(t => t.IsSelected);

            // 并行测试，时间不会因为设备数量增加而线性增长
            return (selectedToolCount * 0.5).ToString("F1");
        }

        private void UpdateSelectedDriveCount()
        {
            SelectedDriveCount = AvailableDrives.Count(d => d.IsSelected);
            OnPropertyChanged(nameof(CanStartTest));
            OnPropertyChanged(nameof(TestSummary));
            OnPropertyChanged(nameof(CanSelectAll));
            OnPropertyChanged(nameof(CanDeselectAll));
        }

        private void Drive_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(DriveInfo.IsSelected) && sender is DriveInfo drive)
            {
                UpdateSelectedDriveCount();
                AddLogEntry(LogLevel.Info, drive.IsSelected
                    ? string.Format(Strings.Log_DriverSelected, drive.Name)
                    : string.Format(Strings.Log_DriverDeselected, drive.Name));
            }
        }

        private void Tool_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TestToolViewModel.IsSelected))
            {
                OnPropertyChanged(nameof(CanStartTest));
                OnPropertyChanged(nameof(HasSelectedTools));
                OnPropertyChanged(nameof(TestSummary));
            }
        }

        partial void OnIsTestRunningChanged(bool value)
        {
            OnPropertyChanged(nameof(CanStartTest));
        }
    }
}

