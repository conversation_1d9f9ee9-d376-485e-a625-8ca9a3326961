<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WindowTitle" xml:space="preserve">
    <value>SD卡自动化测试工具 v1.0</value>
  </data>
  <data name="MenuFile" xml:space="preserve">
    <value>文件(_F)</value>
  </data>
  <data name="MenuFileNewConfig" xml:space="preserve">
    <value>新建配置</value>
  </data>
  <data name="MenuFileOpenConfig" xml:space="preserve">
    <value>打开配置</value>
  </data>
  <data name="MenuFileSaveConfig" xml:space="preserve">
    <value>保存配置</value>
  </data>
  <data name="MenuFileExit" xml:space="preserve">
    <value>退出</value>
  </data>
  <data name="MenuSettings" xml:space="preserve">
    <value>设置(_S)</value>
  </data>
  <data name="MenuSettingsTool" xml:space="preserve">
    <value>测试工具设置</value>
  </data>
  <data name="MenuSettingsReport" xml:space="preserve">
    <value>报告设置</value>
  </data>
  <data name="MenuReport" xml:space="preserve">
    <value>报告(_R)</value>
  </data>
  <data name="MenuReportViewLatest" xml:space="preserve">
    <value>查看最新报告</value>
  </data>
  <data name="MenuReportOpenFolder" xml:space="preserve">
    <value>打开报告文件夹</value>
  </data>
  <data name="MenuHelp" xml:space="preserve">
    <value>帮助(_H)</value>
  </data>
  <data name="MenuHelpUserManual" xml:space="preserve">
    <value>用户手册</value>
  </data>
  <data name="MenuHelpAbout" xml:space="preserve">
    <value>关于</value>
  </data>
  <data name="DeviceSelectionCardTitle" xml:space="preserve">
    <value>🖥️ 设备选择 (多选)</value>
  </data>
  <data name="ButtonSelectAll" xml:space="preserve">
    <value>全选</value>
  </data>
  <data name="ButtonDeselectAll" xml:space="preserve">
    <value>全不选</value>
  </data>
  <data name="LabelCurrentTest" xml:space="preserve">
    <value>当前: {0}</value>
  </data>
  <data name="ButtonRefreshDrives" xml:space="preserve">
    <value>🔄 刷新设备列表</value>
  </data>
  <data name="TestControlCardTitle" xml:space="preserve">
    <value>🎮 并行测试控制</value>
  </data>
  <data name="ButtonStartParallelTest" xml:space="preserve">
    <value>▶️ 开始并行测试</value>
  </data>
  <data name="ButtonStopAllTests" xml:space="preserve">
    <value>⏹️ 停止所有测试</value>
  </data>
  <data name="LabelTestConfiguration" xml:space="preserve">
    <value>📋 测试配置:</value>
  </data>
  <data name="LabelParallelTestInfo" xml:space="preserve">
    <value>💡 并行测试说明:</value>
  </data>
  <data name="TextParallelTestInfo1" xml:space="preserve">
    <value>• 所有选中的设备将同时进行测试</value>
  </data>
  <data name="TextParallelTestInfo2" xml:space="preserve">
    <value>• 每个设备独立运行选中的测试工具</value>
  </data>
  <data name="TextParallelTestInfo3" xml:space="preserve">
    <value>• 可以实时查看每个设备的测试进度</value>
  </data>
  <data name="ToolSelectionCardTitle" xml:space="preserve">
    <value>🔧 测试工具选择</value>
  </data>
  <data name="ButtonAdvancedSettings" xml:space="preserve">
    <value>⚙️ 高级设置</value>
  </data>
  <data name="HeaderGuiTools" xml:space="preserve">
    <value>🖥️ GUI工具</value>
  </data>
  <data name="HeaderCliTools" xml:space="preserve">
    <value>💻 CLI工具</value>
  </data>
  <data name="HeaderHybridTools" xml:space="preserve">
    <value>🔄 混合工具</value>
  </data>
  <data name="ParallelTestProgressCardTitle" xml:space="preserve">
    <value>📊 并行测试进度</value>
  </data>
  <data name="LabelOverallProgress" xml:space="preserve">
    <value>总体进度 (所有设备平均):</value>
  </data>
  <data name="LabelDeviceStatusStats" xml:space="preserve">
    <value>设备状态统计:</value>
  </data>
  <data name="LabelTesting" xml:space="preserve">
    <value>🔄 测试中: </value>
  </data>
  <data name="LabelCompleted" xml:space="preserve">
    <value>✅ 已完成: </value>
  </data>
  <data name="LabelWaiting" xml:space="preserve">
    <value>⚪ 等待中: </value>
  </data>
  <data name="LabelFailed" xml:space="preserve">
    <value>❌ 失败: </value>
  </data>
  <data name="LabelToolStatus" xml:space="preserve">
    <value>测试工具状态:</value>
  </data>
  <data name="TextTip" xml:space="preserve">
    <value>💡 提示: 每个设备独立运行测试</value>
  </data>
  <data name="RealTimeLogCardTitle" xml:space="preserve">
    <value>📝 实时日志</value>
  </data>
  <data name="ButtonClear" xml:space="preserve">
    <value>清空</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="StatusBarCpu" xml:space="preserve">
    <value>CPU: </value>
  </data>
  <data name="StatusBarMemory" xml:space="preserve">
    <value>内存: </value>
  </data>
  <data name="Status_WaitingStart" xml:space="preserve">
    <value>等待开始</value>
  </data>
  <data name="Status_Ready" xml:space="preserve">
    <value>就绪</value>
  </data>
  <data name="Status_NotReady" xml:space="preserve">
    <value>未就绪</value>
  </data>
  <data name="Status_Completed" xml:space="preserve">
    <value>已完成</value>
  </data>
  <data name="Status_PreparingTest" xml:space="preserve">
    <value>正在准备测试...</value>
  </data>
  <data name="Status_TestCompleted" xml:space="preserve">
    <value>测试完成</value>
  </data>
  <data name="Status_TestStopped" xml:space="preserve">
    <value>测试已停止</value>
  </data>
  <data name="Log_FoundRemovableDevice" xml:space="preserve">
    <value>找到 {0} 个可移动存储设备</value>
  </data>
  <data name="TestSummaryFormat" xml:space="preserve">
    <value>• 选中设备: {0} 个
• 选中工具: {1} 个
• 预计时间: 约 {2} 小时
• 测试模式: 并行测试</value>
  </data>
  <data name="Error_RefreshDrives" xml:space="preserve">
    <value>刷新驱动器列表失败: {0}</value>
  </data>
  <data name="Log_TestExecutionFailed" xml:space="preserve">
    <value>测试执行失败: {0}</value>
  </data>
  <data name="Log_BeginTestExecution" xml:space="preserve">
    <value>开始执行测试套件...</value>
  </data>
  <data name="Log_UserStoppedTest" xml:space="preserve">
    <value>用户停止了测试</value>
  </data>
  <data name="Log_SettingSaved" xml:space="preserve">
    <value>设置已保存</value>
  </data>
  <data name="Log_SettingCancelled" xml:space="preserve">
    <value>设置已取消</value>
  </data>
  <data name="Log_OpenSettingWindowsFail" xml:space="preserve">
    <value>打开设置窗口失败: {0}</value>
  </data>
  <data name="Log_ClearLog" xml:space="preserve">
    <value>日志已清空</value>
  </data>
  <data name="Log_SaveLogToFile" xml:space="preserve">
    <value>日志已保存到文件</value>
  </data>
  <data name="Log_SelectedDrives" xml:space="preserve">
    <value>选中的驱动器: {0}</value>
  </data>
  <data name="Log_DriverSelected" xml:space="preserve">
    <value>设备: {0} 已选择</value>
  </data>
  <data name="Log_DriverDeselected" xml:space="preserve">
    <value>设备: {0} 已取消选择</value>
  </data>
  <data name="Log_AllDrivesDeselected" xml:space="preserve">
    <value>已取消选择所有设备</value>
  </data>
  <data name="Log_GenerateReport" xml:space="preserve">
    <value>所有 {0}个设备测试完成, 正在生成报告...</value>
  </data>
  <data name="Log_DriverTestCompleted" xml:space="preserve">
    <value>设备 {0} 所有测试完成</value>
  </data>
  <data name="Log_ToolTestCompleted" xml:space="preserve">
    <value>{0} : {1} 测试完成</value>
  </data>
  <data name="Log_ToolTestBegin" xml:space="preserve">
    <value>{0} : {1} 测试开始</value>
  </data>
  <data name="Log_DriverTestBegin" xml:space="preserve">
    <value>设备 {0} 测试开始</value>
  </data>
</root>