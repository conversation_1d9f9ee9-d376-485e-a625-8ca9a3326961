using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using Microsoft.Extensions.Logging;
using System.IO;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// GUI自动化助手类，整合窗口管理、元素查找、输入模拟和截图功能
/// </summary>
public class AutomationHelper : IDisposable
{
    private readonly ILogger<AutomationHelper> _logger;
    private readonly WindowManager _windowManager;
    private readonly ElementFinder _elementFinder;
    private readonly InputSimulator _inputSimulator;
    private readonly ScreenCapture _screenCapture;
    private bool _disposed = false;

    /// <summary>
    /// 窗口管理器
    /// </summary>
    public WindowManager WindowManager => _windowManager;

    /// <summary>
    /// 元素查找器
    /// </summary>
    public ElementFinder ElementFinder => _elementFinder;

    /// <summary>
    /// 输入模拟器
    /// </summary>
    public InputSimulator InputSimulator => _inputSimulator;

    /// <summary>
    /// 屏幕截图
    /// </summary>
    public ScreenCapture ScreenCapture => _screenCapture;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="windowManager">窗口管理器</param>
    /// <param name="elementFinder">元素查找器</param>
    /// <param name="inputSimulator">输入模拟器</param>
    /// <param name="screenCapture">屏幕截图</param>
    public AutomationHelper(
        ILogger<AutomationHelper> logger,
        WindowManager windowManager,
        ElementFinder elementFinder,
        InputSimulator inputSimulator,
        ScreenCapture screenCapture)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _windowManager = windowManager ?? throw new ArgumentNullException(nameof(windowManager));
        _elementFinder = elementFinder ?? throw new ArgumentNullException(nameof(elementFinder));
        _inputSimulator = inputSimulator ?? throw new ArgumentNullException(nameof(inputSimulator));
        _screenCapture = screenCapture ?? throw new ArgumentNullException(nameof(screenCapture));
    }

    /// <summary>
    /// 等待并查找窗口
    /// </summary>
    /// <param name="title">窗口标题</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="exactMatch">是否精确匹配</param>
    /// <returns>找到的窗口</returns>
    public Window? WaitForWindow(string title, TimeSpan timeout, bool exactMatch = false)
    {
        _logger.LogInformation($"等待窗口出现: {title}");
        var window = _windowManager.FindWindowByTitle(title, timeout, exactMatch);

        if (window != null)
        {
            _logger.LogInformation($"成功找到窗口: {window.Title}");
            _windowManager.ActivateWindow(window);
        }
        else
        {
            _logger.LogError($"未找到窗口: {title}");
        }

        return window;
    }

    /// <summary>
    /// 通过进程ID等待并查找窗口（用于多实例场景）
    /// </summary>
    /// <param name="processId">进程ID</param>
    /// <param name="expectedTitle">期望的窗口标题（用于验证）</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>找到的窗口</returns>
    public Window? WaitForWindowByProcess(int processId, string expectedTitle, TimeSpan timeout)
    {
        _logger.LogInformation($"等待进程 {processId} 的窗口出现，期望标题: {expectedTitle}");
        var window = _windowManager.FindWindowByProcessId(processId, timeout);

        if (window != null)
        {
            // 验证窗口标题是否符合预期
            if (window.Title.Contains(expectedTitle, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation($"成功找到进程 {processId} 的窗口: {window.Title}");
                _windowManager.ActivateWindow(window);
                return window;
            }
            else
            {
                _logger.LogWarning($"进程 {processId} 的窗口标题不匹配，期望: {expectedTitle}，实际: {window.Title}");
            }
        }
        else
        {
            _logger.LogError($"未找到进程 {processId} 的窗口");
        }

        return null;
    }

    /// <summary>
    /// 查找并点击按钮
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="buttonName">按钮名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="doubleClick">是否双击</param>
    /// <returns>是否成功点击</returns>
    public async Task<bool> FindAndClickButton(AutomationElement parent, string buttonName, TimeSpan timeout = default, bool doubleClick = false)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        _logger.LogDebug($"查找并点击按钮: {buttonName}");

        // 先尝试按名称查找按钮
        var button = _elementFinder.FindElementByName(parent, buttonName, timeout);
        
        // 如果没找到，尝试查找Button控件类型中包含该名称的
        if (button == null)
        {
            var buttons = parent.FindAllDescendants(cf => cf.ByControlType(ControlType.Button).And(cf.ByName(buttonName)));
            button = buttons.FirstOrDefault();
        }

        if (button != null)
        {
            return await _inputSimulator.ClickElement(button, doubleClick);
        }

        _logger.LogError($"未找到按钮: {buttonName}");
        return false;
    }
    /// <summary>
    /// 查找并点击按钮
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="automationId">自动化ID</param>
    /// <returns>是否成功点击</returns>
    public async Task<bool> FindAndClickButtonByAutomationId(AutomationElement parent, string automationId, TimeSpan timeout = default, bool doubleClick = false)
    {
        _logger.LogDebug($"查找并点击按钮: {automationId}");
        var button = _elementFinder.FindElementByAutomationId(parent, automationId, timeout);
        if (button != null)
        {
            return await _inputSimulator.ClickElement(button, doubleClick);
        }

        _logger.LogError($"未找到按钮: {automationId}");
        return false;
    }
    /// <summary>
    /// 查找并设置文本框内容
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="textBoxName">文本框名称</param>
    /// <param name="text">要设置的文本</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="clearFirst">是否先清空</param>
    /// <returns>是否成功设置</returns>
    public bool FindAndSetTextBox(AutomationElement parent, string textBoxName, string text, TimeSpan timeout = default, bool clearFirst = true)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        _logger.LogDebug($"查找并设置文本框: {textBoxName} = '{text}'");

        var textBox = _elementFinder.FindElementByName(parent, textBoxName, timeout);

        // 如果没找到，尝试查找Edit控件类型
        if (textBox == null)
        {
            var editElements = parent.FindAllDescendants(cf => cf.ByControlType(ControlType.Edit).And(cf.ByName(textBoxName)));
            textBox = editElements.FirstOrDefault();
        }

        if (textBox != null)
        {
            return _inputSimulator.TypeText(textBox, text, clearFirst);
        }

        _logger.LogError($"未找到文本框: {textBoxName}");
        return false;
    }
    
    /// <summary>
    /// 查找并设置文本框内容
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="automationId">自动化ID</param>
    /// <param name="text">要设置的文本</param>
    /// <param name="clearFirst">是否先清空</param>
    /// <returns>是否成功设置</returns>
    public bool FindAndSetTextBoxByAutomationId(AutomationElement parent, string automationId, string text, bool clearFirst = true)
    {
        _logger.LogDebug($"查找并设置文本框: {automationId} = '{text}'");
        var textBox = _elementFinder.FindElementByAutomationId(parent, automationId, TimeSpan.FromSeconds(10));
        if (textBox != null)
        {
            return _inputSimulator.TypeText(textBox, text, clearFirst);
        }

        _logger.LogError($"未找到文本框: {automationId}");
        return false;
    }

    /// <summary>
    /// 查找并选择下拉框选项
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="comboBoxName">下拉框名称</param>
    /// <param name="optionText">选项文本</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否成功选择</returns>
    public async Task<bool> FindAndSelectComboBoxOption(AutomationElement parent, string comboBoxName, string optionText, TimeSpan timeout = default)
    {
        if (timeout == default)
            timeout = TimeSpan.FromSeconds(10);

        _logger.LogDebug($"查找并选择下拉框选项: {comboBoxName} = '{optionText}'");

        var comboBox = _elementFinder.FindElementByName(parent, comboBoxName, timeout);

        if (comboBox == null)
        {
            var comboElements = parent.FindAllDescendants(cf => cf.ByControlType(ControlType.ComboBox).And(cf.ByName(comboBoxName)));
            comboBox = comboElements.FirstOrDefault();
        }

        if (comboBox != null)
        {
            try
            {
                // 点击下拉框展开
                await _inputSimulator.ClickElement(comboBox);
                Thread.Sleep(500);

                // 查找选项
                var option = _elementFinder.FindElementByName(comboBox, optionText, TimeSpan.FromSeconds(5));
                if (option != null)
                {
                    return await _inputSimulator.ClickElement(option);
                }
                else
                {
                    _logger.LogError($"未找到下拉框选项: {optionText}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"选择下拉框选项失败: {ex.Message}");
                return false;
            }
        }

        _logger.LogError($"未找到下拉框: {comboBoxName}");
        return false;
    }

    /// <summary>
    /// 等待进度条完成
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="progressBarName">进度条名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="checkInterval">检查间隔</param>
    /// <returns>是否在超时时间内完成</returns>
    public bool WaitForProgressBarComplete(AutomationElement parent, string progressBarName, TimeSpan timeout, TimeSpan checkInterval = default)
    {
        if (checkInterval == default)
            checkInterval = TimeSpan.FromSeconds(2);

        _logger.LogInformation($"等待进度条完成: {progressBarName}");

        var progressBar = _elementFinder.FindElementByName(parent, progressBarName, TimeSpan.FromSeconds(10));
        if (progressBar == null)
        {
            var progressBars = parent.FindAllDescendants(cf => cf.ByControlType(ControlType.ProgressBar));
            progressBar = progressBars.FirstOrDefault();
        }

        if (progressBar == null)
        {
            _logger.LogError($"未找到进度条: {progressBarName}");
            return false;
        }

        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            try
            {
                // 检查进度条是否还存在或是否完成
                if (!progressBar.IsAvailable)
                {
                    _logger.LogInformation("进度条已消失，可能已完成");
                    return true;
                }

                // 可以添加更多的完成检查逻辑
                // 例如检查进度条的值属性等
                
                Thread.Sleep((int)checkInterval.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"检查进度条状态时出错: {ex.Message}");
                // 如果出错，可能是进度条已经消失
                return true;
            }
        }

        _logger.LogWarning($"进度条在 {timeout.TotalSeconds} 秒内未完成");
        return false;
    }

    /// <summary>
    /// 截图并保存（用于调试）
    /// </summary>
    /// <param name="window">要截图的窗口</param>
    /// <param name="description">截图描述</param>
    /// <param name="directory">保存目录</param>
    /// <returns>保存的文件路径</returns>
    public string? CaptureForDebugging(Window window, string description, string? directory = null)
    {
        try
        {
            directory ??= Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Screenshots", "Debug");
            var fileName = _screenCapture.GenerateTimestampedFileName($"debug_{description}", "png", directory);
            
            if (_screenCapture.CaptureWindow(window, fileName))
            {
                _logger.LogDebug($"调试截图已保存: {fileName}");
                return fileName;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"调试截图失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 安全地执行操作，出错时截图
    /// </summary>
    /// <param name="action">要执行的操作</param>
    /// <param name="window">相关窗口（用于出错时截图）</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>是否成功执行</returns>
    public bool SafeExecute(Func<bool> action, Window? window, string operationName)
    {
        try
        {
            _logger.LogDebug($"开始执行操作: {operationName}");
            var result = action();
            
            if (result)
            {
                _logger.LogDebug($"操作成功: {operationName}");
            }
            else
            {
                _logger.LogWarning($"操作失败: {operationName}");
                if (window != null)
                {
                    CaptureForDebugging(window, $"failed_{operationName}");
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError($"操作异常: {operationName} - {ex.Message}");
            if (window != null)
            {
                CaptureForDebugging(window, $"error_{operationName}");
            }
            return false;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _windowManager?.Dispose();
            _disposed = true;
        }
    }
}
