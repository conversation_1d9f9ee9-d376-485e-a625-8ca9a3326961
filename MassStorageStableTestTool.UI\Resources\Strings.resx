<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WindowTitle" xml:space="preserve">
    <value>SD Card Automated Test Tool v1.0</value>
  </data>
  <data name="MenuFile" xml:space="preserve">
    <value>File(_F)</value>
  </data>
  <data name="MenuFileNewConfig" xml:space="preserve">
    <value>New Config</value>
  </data>
  <data name="MenuFileOpenConfig" xml:space="preserve">
    <value>Open Config</value>
  </data>
  <data name="MenuFileSaveConfig" xml:space="preserve">
    <value>Save Config</value>
  </data>
  <data name="MenuFileExit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="MenuSettings" xml:space="preserve">
    <value>Settings(_S)</value>
  </data>
  <data name="MenuSettingsTool" xml:space="preserve">
    <value>Test Tool Settings</value>
  </data>
  <data name="MenuSettingsReport" xml:space="preserve">
    <value>Report Settings</value>
  </data>
  <data name="MenuReport" xml:space="preserve">
    <value>Report(_R)</value>
  </data>
  <data name="MenuReportViewLatest" xml:space="preserve">
    <value>View Latest Report</value>
  </data>
  <data name="MenuReportOpenFolder" xml:space="preserve">
    <value>Open Report Folder</value>
  </data>
  <data name="MenuHelp" xml:space="preserve">
    <value>Help(_H)</value>
  </data>
  <data name="MenuHelpUserManual" xml:space="preserve">
    <value>User Manual</value>
  </data>
  <data name="MenuHelpAbout" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="DeviceSelectionCardTitle" xml:space="preserve">
    <value>🖥️ Device Selection 
    (Multi-select)</value>
  </data>
  <data name="ButtonSelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="ButtonDeselectAll" xml:space="preserve">
    <value>Deselect All</value>
  </data>
  <data name="LabelCurrentTest" xml:space="preserve">
    <value>Current: {0}</value>
  </data>
  <data name="ButtonRefreshDrives" xml:space="preserve">
    <value>🔄 Refresh Drive List</value>
  </data>
  <data name="TestControlCardTitle" xml:space="preserve">
    <value>🎮 Parallel Test Control</value>
  </data>
  <data name="ButtonStartParallelTest" xml:space="preserve">
    <value>▶️ Start Tests</value>
  </data>
  <data name="ButtonStopAllTests" xml:space="preserve">
    <value>⏹️ Stop Tests</value>
  </data>
  <data name="LabelTestConfiguration" xml:space="preserve">
    <value>📋 Test Configuration:</value>
  </data>
  <data name="LabelParallelTestInfo" xml:space="preserve">
    <value>💡 Parallel Test Info:</value>
  </data>
  <data name="TextParallelTestInfo1" xml:space="preserve">
    <value>• All selected devices will be tested simultaneously</value>
  </data>
  <data name="TextParallelTestInfo2" xml:space="preserve">
    <value>• Each device runs the selected test tools independently</value>
  </data>
  <data name="TextParallelTestInfo3" xml:space="preserve">
    <value>• You can view the test progress of each device in real time</value>
  </data>
  <data name="ToolSelectionCardTitle" xml:space="preserve">
    <value>🔧 Test Tool Selection</value>
  </data>
  <data name="ButtonAdvancedSettings" xml:space="preserve">
    <value>⚙️ Advanced Settings</value>
  </data>
  <data name="HeaderGuiTools" xml:space="preserve">
    <value>🖥️ GUI Tools</value>
  </data>
  <data name="HeaderCliTools" xml:space="preserve">
    <value>💻 CLI Tools</value>
  </data>
  <data name="HeaderHybridTools" xml:space="preserve">
    <value>🔄 Hybrid Tools</value>
  </data>
  <data name="ParallelTestProgressCardTitle" xml:space="preserve">
    <value>📊 Parallel Test Progress</value>
  </data>
  <data name="LabelOverallProgress" xml:space="preserve">
    <value>Overall Progress (Average of all devices):</value>
  </data>
  <data name="LabelDeviceStatusStats" xml:space="preserve">
    <value>Device Status Stats:</value>
  </data>
  <data name="LabelTesting" xml:space="preserve">
    <value>🔄 Testing: </value>
  </data>
  <data name="LabelCompleted" xml:space="preserve">
    <value>✅ Completed: </value>
  </data>
  <data name="LabelWaiting" xml:space="preserve">
    <value>⚪ Waiting: </value>
  </data>
  <data name="LabelFailed" xml:space="preserve">
    <value>❌ Failed: </value>
  </data>
  <data name="LabelToolStatus" xml:space="preserve">
    <value>Test Tool Status:</value>
  </data>
  <data name="TextTip" xml:space="preserve">
    <value>💡 Tip: Each device runs tests independently</value>
  </data>
  <data name="RealTimeLogCardTitle" xml:space="preserve">
    <value>📝 Real-time Log</value>
  </data>
  <data name="ButtonClear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ButtonSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="StatusBarCpu" xml:space="preserve">
    <value>CPU: </value>
  </data>
  <data name="StatusBarMemory" xml:space="preserve">
    <value>Memory: </value>
  </data>
  <data name="Status_WaitingStart" xml:space="preserve">
    <value>Waiting to start</value>
  </data>
  <data name="Status_Ready" xml:space="preserve">
    <value>Ready</value>
  </data>
  <data name="Status_NotReady" xml:space="preserve">
    <value>Not ready</value>
  </data>
  <data name="Status_Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Status_PreparingTest" xml:space="preserve">
    <value>Preparing test...</value>
  </data>
  <data name="Status_TestCompleted" xml:space="preserve">
    <value>Test completed</value>
  </data>
  <data name="Status_TestStopped" xml:space="preserve">
    <value>Test stopped</value>
  </data>
  <data name="Log_FoundRemovableDevice" xml:space="preserve">
    <value>Found {0} removable storage devices</value>
  </data>
  <data name="TestSummaryFormat" xml:space="preserve">
    <value>• Devices selected: {0}  
• Tools selected: {1}  
• Estimated time: Approx. {2} hours  
• Test Mode: Parallel Testing</value>
  </data>
  <data name="Error_RefreshDrives" xml:space="preserve">
    <value>Failed to refresh drive list: {0}</value>
  </data>
  <data name="Log_TestExecutionFailed" xml:space="preserve">
    <value>Test execution failed: {0}</value>
  </data>
  <data name="Log_BeginTestExecution" xml:space="preserve">
    <value>Starting test execution...</value>
  </data>
  <data name="Log_UserStoppedTest" xml:space="preserve">
    <value>User stopped the test</value>
  </data>
  <data name="Log_SettingSaved" xml:space="preserve">
    <value>Settings saved</value>
  </data>
  <data name="Log_SettingCancelled" xml:space="preserve">
    <value>Settings cancelled</value>
  </data>
  <data name="Log_OpenSettingWindowsFail" xml:space="preserve">
    <value>Failed to open settings window: {0}</value>
  </data>
  <data name="Log_ClearLog" xml:space="preserve">
    <value>Log cleared</value>
  </data>
  <data name="Log_SaveLogToFile" xml:space="preserve">
    <value>Log saved to file</value>
  </data>
  <data name="Log_SelectedDrives" xml:space="preserve">
    <value>{0} devices selected</value>
  </data>
  <data name="Log_DriverSelected" xml:space="preserve">
    <value>Device: {0} selected</value>
  </data>
  <data name="Log_DriverDeselected" xml:space="preserve">
    <value>Device: {0} deselected</value>
  </data>
  <data name="Log_AllDrivesDeselected" xml:space="preserve">
    <value>All devices deselected</value>
  </data>
  <data name="Log_GenerateReport" xml:space="preserve">
    <value>Testing complete for all {0} devices. Generating report...</value>
  </data>
  <data name="Log_DriverTestCompleted" xml:space="preserve">
    <value>All tests completed for device {0}</value>
  </data>
  <data name="Log_ToolTestCompleted" xml:space="preserve">
    <value>{0} : {1} test completed</value>
  </data>
  <data name="Log_ToolTestBegin" xml:space="preserve">
    <value>{0} : {1} test started</value>
  </data>
  <data name="Log_DriverTestBegin" xml:space="preserve">
    <value>Testing started for device {0}</value>
  </data>
</root>